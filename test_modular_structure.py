#!/usr/bin/env python3
"""
Test script to verify the modular structure of the Market Monitoring Agent
"""

import sys
import os
import asyncio
import logging

# Add agents directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_modular_imports():
    """Test that all modular components can be imported"""
    try:
        logger.info("Testing modular imports...")
        
        # Test gateway import
        from run_market_monitoring import MarketMonitoringAgent, check_dependencies, get_system_info
        logger.info("✅ Gateway imports successful")
        
        # Test individual module imports
        from market_monitoring.data_structures import MarketTick, OHLCV, MarketIndicators
        logger.info("✅ Data structures import successful")
        
        from market_monitoring.config_manager import ConfigManager
        logger.info("✅ Config manager import successful")
        
        from market_monitoring.performance_optimizer import PerformanceOptimizer
        logger.info("✅ Performance optimizer import successful")
        
        from market_monitoring.resource_manager import ResourceManager
        logger.info("✅ Resource manager import successful")
        
        from market_monitoring.health_monitor import HealthMonitor
        logger.info("✅ Health monitor import successful")
        
        from market_monitoring.graceful_degradation import GracefulDegradationManager
        logger.info("✅ Graceful degradation import successful")
        
        from market_monitoring.strategy_manager import StrategyManager
        logger.info("✅ Strategy manager import successful")
        
        logger.info("🎉 All modular imports successful!")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False

async def test_gateway_initialization():
    """Test gateway initialization"""
    try:
        logger.info("Testing gateway initialization...")
        
        from run_market_monitoring import MarketMonitoringAgent
        
        # Create agent (this should not fail even without config file)
        agent = MarketMonitoringAgent("config/market_monitoring_config.yaml")
        logger.info("✅ Gateway agent created successfully")
        
        # Test basic properties
        assert hasattr(agent, 'config_manager'), "Agent should have config_manager"
        assert hasattr(agent, 'signal_handlers'), "Agent should have signal_handlers"
        assert hasattr(agent, 'regime_change_handlers'), "Agent should have regime_change_handlers"
        logger.info("✅ Gateway agent has required attributes")
        
        logger.info("🎉 Gateway initialization test successful!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Gateway initialization error: {e}")
        return False

async def test_dependency_check():
    """Test dependency checking"""
    try:
        logger.info("Testing dependency check...")
        
        from run_market_monitoring import check_dependencies
        
        deps = check_dependencies()
        logger.info(f"📦 Dependencies found: {list(deps.keys())}")
        
        for dep, available in deps.items():
            status = "✅" if available else "❌"
            logger.info(f"   {status} {dep}")
        
        logger.info("🎉 Dependency check test successful!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Dependency check error: {e}")
        return False

async def test_system_info():
    """Test system information gathering"""
    try:
        logger.info("Testing system info...")
        
        from run_market_monitoring import get_system_info
        
        system_info = get_system_info()
        logger.info(f"💻 System info: {system_info}")
        
        required_keys = ['cpu_percent', 'memory_percent', 'disk_usage', 'cpu_count']
        for key in required_keys:
            assert key in system_info, f"System info should contain {key}"
        
        logger.info("🎉 System info test successful!")
        return True
        
    except Exception as e:
        logger.error(f"❌ System info error: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("=" * 60)
    logger.info("🧪 TESTING MODULAR MARKET MONITORING STRUCTURE")
    logger.info("=" * 60)
    
    tests = [
        ("Modular Imports", test_modular_imports),
        ("Gateway Initialization", test_gateway_initialization),
        ("Dependency Check", test_dependency_check),
        ("System Info", test_system_info)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status} - {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! Modular structure is working correctly.")
        return True
    else:
        logger.error(f"❌ {total - passed} tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
