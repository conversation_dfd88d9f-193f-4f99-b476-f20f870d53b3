#!/usr/bin/env python3
"""
Enhanced Market Monitoring Agent Runner
Production-ready runner with comprehensive error handling, monitoring, and optimization
"""

import os
import sys
import asyncio
import logging
import signal
import argparse
from datetime import datetime
from typing import Optional
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import enhanced modules
from market_monitoring.enhanced_runner import EnhancedMarketMonitoringRunner
from market_monitoring.data_structures import MarketRegime, TradingSignal
from market_monitoring.config_manager import ConfigManager
from market_monitoring.performance_optimizer import PerformanceOptimizer
from market_monitoring.resource_manager import ResourceManager
from market_monitoring.health_monitor import HealthMonitor
from market_monitoring.graceful_degradation import GracefulDegradationManager
from market_monitoring.strategy_manager import StrategyManager

logger = logging.getLogger(__name__)


def check_dependencies() -> dict:
    """Check required dependencies"""
    dependencies = {}

    try:
        import polars
        dependencies['polars'] = True
    except ImportError:
        dependencies['polars'] = False

    try:
        import pandas
        dependencies['pandas'] = True
    except ImportError:
        dependencies['pandas'] = False

    try:
        import numpy
        dependencies['numpy'] = True
    except ImportError:
        dependencies['numpy'] = False

    try:
        from SmartApi import SmartConnect
        dependencies['smartapi'] = True
    except ImportError:
        dependencies['smartapi'] = False

    try:
        import telegram
        dependencies['telegram'] = True
    except ImportError:
        dependencies['telegram'] = False

    return dependencies


def get_system_info() -> dict:
    """Get system information"""
    try:
        import psutil

        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'cpu_count': psutil.cpu_count()
        }
    except ImportError:
        return {
            'cpu_percent': 0,
            'memory_percent': 0,
            'disk_usage': 0,
            'cpu_count': 1
        }


class MarketMonitoringRunner:
    """
    Enhanced Production runner for Market Monitoring Agent

    Features:
    - Enhanced graceful startup and shutdown
    - Advanced signal handling
    - Intelligent error recovery
    - Comprehensive performance monitoring
    - Advanced health checks
    - Resource management
    - Graceful degradation
    """

    def __init__(self, config_path: str = "config/market_monitoring_config.yaml"):
        """Initialize enhanced runner"""
        self.config_path = config_path
        self.enhanced_runner = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()

        # Initialize managers
        self.config_manager = ConfigManager()
        self.performance_optimizer = None
        self.resource_manager = None
        self.health_monitor = None
        self.degradation_manager = None
        self.strategy_manager = None

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info("[INIT] Enhanced Market Monitoring Runner initialized")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()
    
    async def start(self):
        """Start the Enhanced Market Monitoring Agent"""
        try:
            logger.info("[CONFIG] Starting Enhanced Market Monitoring Agent...")

            # Check dependencies
            await self._check_dependencies()

            # Check system resources
            await self._check_system_resources()

            # Initialize enhanced components
            await self._initialize_enhanced_components()

            # Create and setup enhanced runner
            self.enhanced_runner = EnhancedMarketMonitoringRunner(self.config_path)
            await self.enhanced_runner.initialize()

            # Add custom handlers
            await self._setup_enhanced_handlers()

            # Start monitoring
            self.is_running = True
            logger.info("[SUCCESS] Enhanced Market Monitoring Agent started successfully")

            # Run main loop
            await self._run_enhanced_main_loop()

        except Exception as e:
            logger.error(f"[ERROR] Failed to start Enhanced Market Monitoring Agent: {e}")
            raise
    
    async def stop(self):
        """Stop the Enhanced Market Monitoring Agent"""
        try:
            logger.info("[STOP] Stopping Enhanced Market Monitoring Agent...")

            self.is_running = False

            # Stop enhanced runner
            if self.enhanced_runner:
                await self.enhanced_runner.stop()

            # Stop enhanced components
            await self._shutdown_enhanced_components()

            logger.info("[SUCCESS] Enhanced Market Monitoring Agent stopped successfully")

        except Exception as e:
            logger.error(f"[ERROR] Error stopping enhanced agent: {e}")

    async def _initialize_enhanced_components(self):
        """Initialize enhanced components"""
        try:
            logger.info("[INIT] Initializing enhanced components...")

            # Load configuration
            config = self.config_manager.load_config(self.config_path)

            # Initialize performance optimizer
            self.performance_optimizer = PerformanceOptimizer(config)
            await self.performance_optimizer.initialize()

            # Initialize resource manager
            self.resource_manager = ResourceManager(config)
            await self.resource_manager.start_monitoring()

            # Initialize health monitor
            self.health_monitor = HealthMonitor(config)
            await self.health_monitor.start_monitoring()

            # Initialize degradation manager
            self.degradation_manager = GracefulDegradationManager(config)

            # Initialize strategy manager
            self.strategy_manager = StrategyManager(config)

            logger.info("[SUCCESS] Enhanced components initialized")

        except Exception as e:
            logger.error(f"[ERROR] Error initializing enhanced components: {e}")
            raise

    async def _shutdown_enhanced_components(self):
        """Shutdown enhanced components"""
        try:
            logger.info("[SHUTDOWN] Shutting down enhanced components...")

            if self.performance_optimizer:
                await self.performance_optimizer.shutdown()

            if self.resource_manager:
                await self.resource_manager.stop_monitoring()

            if self.health_monitor:
                await self.health_monitor.stop_monitoring()

            logger.info("[SUCCESS] Enhanced components shutdown completed")

        except Exception as e:
            logger.error(f"[ERROR] Error shutting down enhanced components: {e}")
    
    async def _check_dependencies(self):
        """Check required dependencies with enhanced validation"""
        logger.info("[DEBUG] Checking dependencies...")

        deps = check_dependencies()
        missing_deps = [dep for dep, available in deps.items() if not available]

        if missing_deps:
            logger.error(f"[ERROR] Missing dependencies: {missing_deps}")
            logger.info("📦 Install missing dependencies:")

            if 'smartapi' in missing_deps:
                logger.info("   pip install smartapi-python pyotp")
            if 'telegram' in missing_deps:
                logger.info("   pip install python-telegram-bot")
            if 'polars' in missing_deps:
                logger.info("   pip install polars[gpu]")  # Use GPU-accelerated version
            if 'pandas' in missing_deps:
                logger.info("   pip install pandas")
            if 'numpy' in missing_deps:
                logger.info("   pip install numpy")

            # Configure graceful degradation for missing optional services
            if self.degradation_manager:
                await self.degradation_manager.configure_fallbacks(missing_deps)

            # Only fail for critical dependencies
            critical_deps = ['polars', 'numpy']  # Reduced critical deps, pandas is optional
            critical_missing = [dep for dep in missing_deps if dep in critical_deps]

            if critical_missing:
                raise RuntimeError(f"Critical dependencies missing: {critical_missing}")

        logger.info("[SUCCESS] Enhanced dependencies check completed")
    
    async def _check_system_resources(self):
        """Check system resources with enhanced monitoring"""
        logger.info("[SYSTEM] Checking system resources...")

        if self.resource_manager:
            # Use enhanced resource manager
            resource_status = await self.resource_manager.check_system_resources()

            # Check memory
            memory_percent = resource_status.get('memory_percent', 0)
            if memory_percent > 90:
                logger.warning(f"[WARN]  High memory usage: {memory_percent}%")
            elif memory_percent > 80:
                logger.info(f"[INFO]  Moderate memory usage: {memory_percent}%")

            # Check CPU
            cpu_percent = resource_status.get('cpu_percent', 0)
            if cpu_percent > 90:
                logger.warning(f"[WARN]  High CPU usage: {cpu_percent}%")
            elif cpu_percent > 80:
                logger.info(f"[INFO]  Moderate CPU usage: {cpu_percent}%")

            # Check disk space
            disk_percent = resource_status.get('disk_percent', 0)
            if disk_percent > 90:
                logger.warning(f"[WARN]  Low disk space: {disk_percent}% used")
            elif disk_percent > 80:
                logger.info(f"[INFO]  Moderate disk usage: {disk_percent}%")

            # Log comprehensive system info
            logger.info(f"[SYSTEM] Enhanced: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%, "
                       f"Disk {disk_percent:.1f}%, Available Memory: {resource_status.get('memory_available_gb', 0):.1f}GB")

            # Check if resources are adequate
            if not resource_status.get('adequate', True):
                logger.warning("[WARN]  System resources may be insufficient for optimal performance")
        else:
            # Fallback to basic system info
            system_info = get_system_info()
            memory_percent = system_info.get('memory_percent', 0)
            cpu_percent = system_info.get('cpu_percent', 0)
            disk_usage = system_info.get('disk_usage', 0)

            logger.info(f"[SYSTEM] Basic: CPU {cpu_percent}%, Memory {memory_percent}%, Disk {disk_usage}%")
    
    async def _setup_enhanced_handlers(self):
        """Setup enhanced event handlers with performance tracking"""

        async def enhanced_signal_handler(signal: TradingSignal):
            """Enhanced signal handler with validation and performance tracking"""
            try:
                # Validate signal through strategy manager
                if self.strategy_manager:
                    # Update strategy performance tracking
                    # This would be called later when we know the outcome
                    pass

                # Process through degradation manager if needed
                if self.degradation_manager:
                    signal = await self.degradation_manager.process_signal(signal)

                # Enhanced logging with more context
                logger.info(f"[SIGNAL] {signal.symbol} | {signal.strategy} | {signal.action} | "
                           f"Price: ₹{signal.price:.2f} | Target: ₹{signal.target:.2f} | "
                           f"SL: ₹{signal.stop_loss:.2f} | Confidence: {signal.confidence:.1%} | "
                           f"Regime: {getattr(signal, 'market_regime', 'unknown')}")

                # Record performance metrics
                if self.performance_optimizer:
                    # Track signal generation performance
                    pass

                # Add custom signal processing here
                # e.g., send to order management system, update database, etc.

            except Exception as e:
                logger.error(f"[ERROR] Error in enhanced signal handler: {e}")

        async def enhanced_regime_change_handler(old_regime: Optional[MarketRegime], new_regime: MarketRegime):
            """Enhanced regime change handler with strategy adjustment"""
            try:
                old_regime_name = old_regime.regime if old_regime else "None"

                logger.info(f"[REGIME] {old_regime_name} → {new_regime.regime} | "
                           f"Confidence: {new_regime.confidence:.1%} | "
                           f"Volatility: {new_regime.volatility_level} | "
                           f"Breadth: {new_regime.market_breadth:.1f}%")

                # Update strategy manager with new regime
                if self.strategy_manager:
                    # Strategy manager can adjust parameters based on regime
                    pass

                # Update resource manager for regime-based scaling
                if self.resource_manager:
                    # Adjust resource allocation based on market regime
                    if new_regime.volatility_level == 'high':
                        logger.info("[RESOURCE] High volatility detected, adjusting resource allocation")

                # Record health metrics
                if self.health_monitor:
                    self.health_monitor.record_operation(success=True)

                # Add custom regime change processing here
                # e.g., adjust strategy parameters, send alerts, etc.

            except Exception as e:
                logger.error(f"[ERROR] Error in enhanced regime change handler: {e}")

        # Register enhanced handlers with the enhanced runner
        if self.enhanced_runner and hasattr(self.enhanced_runner, 'agent'):
            if hasattr(self.enhanced_runner.agent, 'add_signal_handler'):
                self.enhanced_runner.agent.add_signal_handler(enhanced_signal_handler)
            if hasattr(self.enhanced_runner.agent, 'add_regime_change_handler'):
                self.enhanced_runner.agent.add_regime_change_handler(enhanced_regime_change_handler)

        logger.info("[CONFIG] Enhanced handlers registered")
    
    async def _run_enhanced_main_loop(self):
        """Run enhanced main monitoring loop with advanced features"""
        try:
            # Check if this is part of a workflow (demo=False means full workflow)
            demo_mode = os.getenv('DEMO_MODE', 'true').lower() == 'true'
            workflow_mode = os.getenv('WORKFLOW_MODE', 'false').lower() == 'true'

            if not demo_mode and workflow_mode:
                # Enhanced workflow mode with performance optimization
                logger.info("[WORKFLOW] Running in enhanced workflow mode")

                # Determine parameters based on testing mode
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                download_days_back = 5 if testing_mode else 35
                max_symbols_to_download = int(os.getenv('MAX_SYMBOLS', '500')) if not testing_mode else int(os.getenv('MAX_SYMBOLS', '20'))

                # Optimize resource allocation for symbol count
                if self.resource_manager:
                    optimization = await self.resource_manager.optimize_for_symbol_count(max_symbols_to_download)
                    logger.info(f"[OPTIMIZATION] Resource optimization: {optimization}")

                # Step 1: Enhanced historical data download with incremental loading
                logger.info(f"[PRE-MARKET] Starting optimized historical data download for {download_days_back} days and {max_symbols_to_download} symbols...")

                if self.enhanced_runner and hasattr(self.enhanced_runner, 'agent'):
                    download_success = await self.enhanced_runner.agent.download_live_historical_data(
                        days_back=download_days_back,
                        max_symbols=max_symbols_to_download,
                        testing_mode=testing_mode
                    )
                else:
                    logger.warning("[WORKFLOW] Enhanced runner not available, skipping download")
                    download_success = True

                if not download_success:
                    logger.error("[ERROR] Enhanced historical data download failed")
                    # Try graceful degradation
                    if self.degradation_manager:
                        await self.degradation_manager.handle_service_failure('data_download', Exception("Download failed"))
                    return

                logger.info("[SUCCESS] Enhanced historical data download completed")

                # Step 2: Start enhanced live monitoring
                logger.info("[LIVE] Starting enhanced live market monitoring...")

                # Start enhanced runner
                if self.enhanced_runner:
                    await self.enhanced_runner.start()

                # Run for specified duration with enhanced monitoring
                timeout_duration = 30  # 30 seconds for live monitoring in workflow
                logger.info(f"[WORKFLOW] Running enhanced monitoring for {timeout_duration} seconds...")

                try:
                    # Wait for the specified duration or shutdown signal
                    await asyncio.wait_for(self.shutdown_event.wait(), timeout=timeout_duration)
                    logger.info("[WORKFLOW] Enhanced monitoring stopped by shutdown signal")
                except asyncio.TimeoutError:
                    logger.info(f"[WORKFLOW] Enhanced monitoring completed {timeout_duration} second session")

                return

            # Enhanced continuous mode for demo/standalone operation
            logger.info("[CONTINUOUS] Starting enhanced continuous monitoring mode")

            # Start enhanced runner
            if self.enhanced_runner:
                await self.enhanced_runner.start()

            # Start enhanced health monitoring
            health_task = asyncio.create_task(self._enhanced_health_monitoring_loop())

            # Start performance monitoring
            performance_task = asyncio.create_task(self._performance_monitoring_loop())

            # Wait for shutdown signal
            done, pending = await asyncio.wait(
                [health_task, performance_task, asyncio.create_task(self.shutdown_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Cancel pending tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            logger.info("[STOP] Enhanced main monitoring loop completed")

        except Exception as e:
            logger.error(f"[ERROR] Error in enhanced main loop: {e}")
            # Try graceful degradation
            if self.degradation_manager:
                await self.degradation_manager.handle_service_failure('main_loop', e)
            raise
    
    async def _enhanced_health_monitoring_loop(self):
        """Enhanced health monitoring loop with comprehensive checks"""
        while self.is_running:
            try:
                # Perform comprehensive health check
                if self.health_monitor and self.enhanced_runner:
                    health_status = await self.health_monitor.perform_health_check(self.enhanced_runner.agent)

                    # Log health status based on severity
                    if health_status['overall'] == 'healthy':
                        logger.debug(f"[HEALTH] System healthy - Score: {health_status['score']}")
                    elif health_status['overall'] == 'warning':
                        logger.warning(f"[HEALTH] System warnings - Score: {health_status['score']}, Issues: {len(health_status['issues'])}")
                    else:
                        logger.error(f"[HEALTH] System critical - Score: {health_status['score']}, Issues: {len(health_status['issues'])}")

                        # Handle critical issues
                        for issue in health_status['issues']:
                            if issue['severity'] == 'critical':
                                logger.error(f"[CRITICAL] {issue['message']}")

                                # Trigger recovery mechanisms
                                if self.degradation_manager:
                                    await self.degradation_manager.handle_service_failure(
                                        issue['type'],
                                        Exception(issue['message'])
                                    )

                # Check resource usage and scaling
                if self.resource_manager:
                    resource_status = await self.resource_manager.check_resource_usage()

                    # Log resource summary
                    logger.debug(f"[RESOURCES] CPU: {resource_status.get('cpu_percent', 0):.1f}%, "
                               f"Memory: {resource_status.get('memory_percent', 0):.1f}%, "
                               f"Disk: {resource_status.get('disk_percent', 0):.1f}%")

                # Wait for next health check with adaptive intervals
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                health_interval = 10 if testing_mode else 30  # More frequent checks
                await asyncio.sleep(health_interval)

            except Exception as e:
                logger.error(f"[ERROR] Error in enhanced health monitoring: {e}")
                if self.health_monitor:
                    self.health_monitor.record_operation(success=False)
                await asyncio.sleep(60)

    async def _performance_monitoring_loop(self):
        """Performance monitoring loop with optimization"""
        while self.is_running:
            try:
                # Get performance metrics from all components
                performance_data = {}

                if self.performance_optimizer:
                    performance_data['optimizer'] = self.performance_optimizer.get_performance_metrics()

                if self.resource_manager:
                    performance_data['resources'] = self.resource_manager.get_resource_summary()

                if self.strategy_manager:
                    performance_data['strategies'] = self.strategy_manager.get_strategy_performance()

                if self.health_monitor:
                    performance_data['health'] = self.health_monitor.get_health_summary()

                # Log performance summary
                if performance_data:
                    cache_hit_rate = performance_data.get('optimizer', {}).get('cache_hit_rate', 0)
                    current_score = performance_data.get('health', {}).get('current_score', 0)

                    logger.info(f"[PERFORMANCE] Cache Hit Rate: {cache_hit_rate:.1%}, "
                               f"Health Score: {current_score}, "
                               f"Components: {len(performance_data)}")

                # Trigger optimizations if needed
                if self.performance_optimizer:
                    # Clear old cache entries
                    self.performance_optimizer.clear_cache(older_than_hours=2)

                # Wait for next performance check
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                perf_interval = 30 if testing_mode else 120  # 30s in testing, 2min in production
                await asyncio.sleep(perf_interval)

            except Exception as e:
                logger.error(f"[ERROR] Error in performance monitoring: {e}")
                await asyncio.sleep(120)
    
    async def run(self):
        """Main run method"""
        try:
            await self.start()
        except KeyboardInterrupt:
            logger.info("[STOP] Received keyboard interrupt")
        except Exception as e:
            logger.error(f"[ERROR] Runtime error: {e}")
            raise
        finally:
            await self.stop()

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/market_monitoring_runner.log')
        ]
    )

def main():
    """Enhanced main entry point"""
    parser = argparse.ArgumentParser(description='Enhanced Market Monitoring Agent Runner')
    parser.add_argument('--config', '-c',
                       default='config/market_monitoring_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--log-level', '-l',
                       default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    parser.add_argument('--check-deps', action='store_true',
                       help='Check dependencies and exit')
    parser.add_argument('--validate-config', action='store_true',
                       help='Validate configuration and exit')
    parser.add_argument('--performance-report', action='store_true',
                       help='Generate performance report and exit')

    args = parser.parse_args()

    # Setup enhanced logging
    setup_logging(args.log_level)

    # Check dependencies only
    if args.check_deps:
        deps = check_dependencies()
        print("\n📦 Enhanced Dependency Status:")
        for dep, available in deps.items():
            status = "✅ [AVAILABLE]" if available else "❌ [MISSING]"
            print(f"   {status} {dep}")

        missing = [dep for dep, available in deps.items() if not available]
        if missing:
            print(f"\n❌ Missing dependencies: {missing}")
            print("\n📦 Installation commands:")
            if 'polars' in missing:
                print("   pip install polars[gpu]")
            if 'smartapi' in missing:
                print("   pip install smartapi-python pyotp")
            if 'telegram' in missing:
                print("   pip install python-telegram-bot")
            sys.exit(1)
        else:
            print("\n✅ All dependencies available")
            sys.exit(0)

    # Configuration validation mode
    if args.validate_config:
        try:
            from market_monitoring.config_validator import ConfigValidator
            validator = ConfigValidator()
            report = validator.generate_validation_report(args.config)
            print(report)
            return
        except ImportError:
            logger.error("❌ Configuration validator not available")
            return
        except Exception as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            return

    # Performance report mode
    if args.performance_report:
        logger.info("📊 Performance report mode not yet implemented")
        return

    # Check if config file exists
    if not os.path.exists(args.config):
        logger.error(f"❌ Configuration file not found: {args.config}")
        sys.exit(1)

    # Create and run enhanced agent
    runner = MarketMonitoringRunner(args.config)

    try:
        logger.info("=" * 60)
        logger.info("🚀 ENHANCED MARKET MONITORING AGENT")
        logger.info("=" * 60)
        logger.info(f"📁 Configuration: {args.config}")
        logger.info(f"📊 Log Level: {args.log_level}")
        logger.info(f"⏰ Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

        asyncio.run(runner.run())

    except KeyboardInterrupt:
        logger.info("🛑 Enhanced Market Monitoring Agent stopped by user")
    except Exception as e:
        logger.error(f"❌ Fatal error in enhanced agent: {e}")
        logger.exception("Full error traceback:")
        sys.exit(1)
    finally:
        logger.info("=" * 60)
        logger.info("🏁 ENHANCED MARKET MONITORING AGENT SHUTDOWN")
        logger.info(f"⏰ End Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

if __name__ == "__main__":
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  WARNING: Virtual environment not detected!")
        print("   Consider activating venv: source /media/jmk/BKP/Documents/Option/.venv/bin/activate")
        print()

    main()
