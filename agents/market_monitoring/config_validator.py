# agents/market_monitoring/config_validator.py

import logging
from typing import Dict, Any
from agents.market_monitoring.constants import DEFAULT_CONFIG_VALUES

logger = logging.getLogger(__name__)

class ConfigValidator:
    """
    Validates the market monitoring agent configuration, ensuring all required
    fields are present and have sensible values.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.errors = []

    def validate(self) -> bool:
        """
        Performs a full validation of the configuration.
        Returns True if valid, False otherwise.
        """
        self._validate_smartapi_config()
        self._validate_market_data_config()
        self._validate_environment_config()
        self._validate_strategy_triggering_config()
        self._validate_notifications_config()
        self._validate_logging_config()
        self._validate_storage_config()
        self._validate_performance_config()
        self._validate_error_handling_config()

        if self.errors:
            for error in self.errors:
                logger.error(f"[CONFIG_VALIDATION_ERROR] {error}")
            logger.error("[CONFIG_VALIDATION] Configuration validation failed. Please check the errors above.")
            return False
        
        logger.info("[CONFIG_VALIDATION] Configuration validated successfully.")
        return True

    def _get_value(self, path: str, default: Any = None) -> Any:
        """Helper to get a value from config by dot-separated path."""
        keys = path.split('.')
        current = self.config
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current

    def _add_error(self, message: str):
        self.errors.append(message)

    def _validate_smartapi_config(self):
        smartapi_config = self.config.get('smartapi', {})
        if not smartapi_config:
            self._add_error("SmartAPI configuration is missing.")
            return

        required_fields = ['api_key', 'username', 'password', 'totp_token']
        for field in required_fields:
            if not smartapi_config.get(field):
                self._add_error(f"SmartAPI: '{field}' is missing or empty.")
        
        # Validate websocket specific settings
        websocket_config = smartapi_config.get('websocket', {})
        if not isinstance(websocket_config.get('reconnect_attempts'), int) or websocket_config['reconnect_attempts'] <= 0:
            self._add_error("SmartAPI: 'websocket.reconnect_attempts' must be a positive integer.")
        if not isinstance(websocket_config.get('reconnect_delay'), (int, float)) or websocket_config['reconnect_delay'] <= 0:
            self._add_error("SmartAPI: 'websocket.reconnect_delay' must be a positive number.")

    def _validate_market_data_config(self):
        market_data_config = self.config.get('market_data', {})
        if not market_data_config:
            self._add_error("Market data configuration is missing.")
            return

        if not isinstance(market_data_config.get('timeframes'), list) or not market_data_config['timeframes']:
            self._add_error("Market Data: 'timeframes' must be a non-empty list.")
        
        if not isinstance(market_data_config.get('max_candles_per_timeframe'), int) or market_data_config['max_candles_per_timeframe'] <= 0:
            self._add_error("Market Data: 'max_candles_per_timeframe' must be a positive integer.")

        # Validate indicators config
        indicators_config = market_data_config.get('indicators', {})
        if not isinstance(indicators_config.get('ema_periods'), list):
            self._add_error("Market Data: 'indicators.ema_periods' must be a list.")
        if not isinstance(indicators_config.get('rsi_periods'), list):
            self._add_error("Market Data: 'indicators.rsi_periods' must be a list.")
        if not isinstance(indicators_config.get('atr_period'), int) or indicators_config['atr_period'] <= 0:
            self._add_error("Market Data: 'indicators.atr_period' must be a positive integer.")
        
        macd_config = indicators_config.get('macd_config', {})
        if not isinstance(macd_config.get('fast'), int) or macd_config['fast'] <= 0:
            self._add_error("Market Data: 'indicators.macd_config.fast' must be a positive integer.")
        if not isinstance(macd_config.get('slow'), int) or macd_config['slow'] <= 0:
            self._add_error("Market Data: 'indicators.macd_config.slow' must be a positive integer.")
        if not isinstance(macd_config.get('signal'), int) or macd_config['signal'] <= 0:
            self._add_error("Market Data: 'indicators.macd_config.signal' must be a positive integer.")

    def _validate_environment_config(self):
        env_config = self.config.get('environment', {})
        if not env_config:
            self._add_error("Environment configuration is missing.")
            return

        regime_config = env_config.get('regime_detection', {})
        if not isinstance(regime_config.get('breadth_threshold'), (int, float)) or not (0 <= regime_config['breadth_threshold'] <= 100):
            self._add_error("Environment: 'regime_detection.breadth_threshold' must be a number between 0 and 100.")

        volatility_config = env_config.get('volatility', {})
        if not isinstance(volatility_config.get('high_vol_threshold'), (int, float)) or not (0 <= volatility_config['high_vol_threshold'] <= 100):
            self._add_error("Environment: 'volatility.high_vol_threshold' must be a number between 0 and 100.")
        if not isinstance(volatility_config.get('low_vol_threshold'), (int, float)) or not (0 <= volatility_config['low_vol_threshold'] <= 100):
            self._add_error("Environment: 'volatility.low_vol_threshold' must be a number between 0 and 100.")

    def _validate_strategy_triggering_config(self):
        strategy_config = self.config.get('strategy_triggering', {})
        if not strategy_config:
            self._add_error("Strategy triggering configuration is missing.")
            return

        ai_model_config = strategy_config.get('ai_model', {})
        if not isinstance(ai_model_config.get('enable'), bool):
            self._add_error("Strategy Triggering: 'ai_model.enable' must be a boolean.")
        if not isinstance(ai_model_config.get('confidence_threshold'), (int, float)) or not (0 <= ai_model_config['confidence_threshold'] <= 1):
            self._add_error("Strategy Triggering: 'ai_model.confidence_threshold' must be a number between 0 and 1.")

        risk_management_config = strategy_config.get('risk_management', {})
        if not isinstance(risk_management_config.get('max_position_size_percent'), (int, float)) or risk_management_config['max_position_size_percent'] <= 0:
            self._add_error("Strategy Triggering: 'risk_management.max_position_size_percent' must be a positive number.")
        if not isinstance(risk_management_config.get('max_daily_trades'), int) or risk_management_config['max_daily_trades'] < 0:
            self._add_error("Strategy Triggering: 'risk_management.max_daily_trades' must be a non-negative integer.")

    def _validate_notifications_config(self):
        notifications_config = self.config.get('notifications', {})
        if not notifications_config:
            self._add_error("Notifications configuration is missing.")
            return

        telegram_config = notifications_config.get('telegram', {})
        if not isinstance(telegram_config.get('enable'), bool):
            self._add_error("Notifications: 'telegram.enable' must be a boolean.")
        if telegram_config.get('enable'):
            if not telegram_config.get('bot_token') or telegram_config['bot_token'] == "YOUR_BOT_TOKEN":
                self._add_error("Notifications: Telegram is enabled but 'bot_token' is missing or default.")
            if not telegram_config.get('chat_id') or telegram_config['chat_id'] == "YOUR_CHAT_ID":
                self._add_error("Notifications: Telegram is enabled but 'chat_id' is missing or default.")

        email_config = notifications_config.get('email', {})
        if not isinstance(email_config.get('enable'), bool):
            self._add_error("Notifications: 'email.enable' must be a boolean.")
        if email_config.get('enable'):
            required_fields = ['sender_email', 'sender_password', 'receiver_email', 'smtp_server', 'smtp_port']
            for field in required_fields:
                if not email_config.get(field):
                    self._add_error(f"Notifications: Email is enabled but '{field}' is missing or empty.")
            if not isinstance(email_config.get('smtp_port'), int) or email_config['smtp_port'] <= 0:
                self._add_error("Notifications: 'email.smtp_port' must be a positive integer.")

    def _validate_logging_config(self):
        logging_config = self.config.get('logging', {})
        if not logging_config:
            self._add_error("Logging configuration is missing.")
            return

        if logging_config.get('level') not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            self._add_error("Logging: 'level' must be one of DEBUG, INFO, WARNING, ERROR, CRITICAL.")

        file_logging_config = logging_config.get('file_logging', {})
        if not isinstance(file_logging_config.get('enable'), bool):
            self._add_error("Logging: 'file_logging.enable' must be a boolean.")
        if not file_logging_config.get('log_dir'):
            self._add_error("Logging: 'file_logging.log_dir' is missing or empty.")

        signal_logging_config = logging_config.get('signal_logging', {})
        if not isinstance(signal_logging_config.get('enable'), bool):
            self._add_error("Logging: 'signal_logging.enable' must be a boolean.")
        if not signal_logging_config.get('signal_log_file'):
            self._add_error("Logging: 'signal_logging.signal_log_file' is missing or empty.")
        if not isinstance(signal_logging_config.get('include_market_context'), bool):
            self._add_error("Logging: 'signal_logging.include_market_context' must be a boolean.")

        performance_logging_config = logging_config.get('performance_logging', {})
        if not isinstance(performance_logging_config.get('enable'), bool):
            self._add_error("Logging: 'performance_logging.enable' must be a boolean.")
        if not performance_logging_config.get('metrics_file'):
            self._add_error("Logging: 'performance_logging.metrics_file' is missing or empty.")
        if not isinstance(performance_logging_config.get('log_interval'), int) or performance_logging_config['log_interval'] <= 0:
            self._add_error("Logging: 'performance_logging.log_interval' must be a positive integer.")

    def _validate_storage_config(self):
        storage_config = self.config.get('storage', {})
        if not storage_config:
            self._add_error("Storage configuration is missing.")
            return

        realtime_data_config = storage_config.get('realtime_data', {})
        if not realtime_data_config.get('storage_path'):
            self._add_error("Storage: 'realtime_data.storage_path' is missing or empty.")

        signals_config = storage_config.get('signals', {})
        if not signals_config.get('storage_path'):
            self._add_error("Storage: 'signals.storage_path' is missing or empty.")

        market_context_config = storage_config.get('market_context', {})
        if not market_context_config.get('storage_path'):
            self._add_error("Storage: 'market_context.storage_path' is missing or empty.")

    def _validate_performance_config(self):
        performance_config = self.config.get('performance', {})
        if not performance_config:
            self._add_error("Performance configuration is missing.")
            return

        memory_config = performance_config.get('memory', {})
        if not isinstance(memory_config.get('cleanup_interval'), int) or memory_config['cleanup_interval'] <= 0:
            self._add_error("Performance: 'memory.cleanup_interval' must be a positive integer.")

    def _validate_error_handling_config(self):
        error_handling_config = self.config.get('error_handling', {})
        if not error_handling_config:
            self._add_error("Error handling configuration is missing.")
            return

        shutdown_config = error_handling_config.get('shutdown', {})
        if not isinstance(shutdown_config.get('save_state'), bool):
            self._add_error("Error Handling: 'shutdown.save_state' must be a boolean.")

        alerting_config = error_handling_config.get('alerting', {})
        if not isinstance(alerting_config.get('enable_email_alerts'), bool):
            self._add_error("Error Handling: 'alerting.enable_email_alerts' must be a boolean.")
        if not isinstance(alerting_config.get('enable_telegram_alerts'), bool):
            self._add_error("Error Handling: 'alerting.enable_telegram_alerts' must be a boolean.")
        if not isinstance(alerting_config.get('critical_error_threshold'), int) or alerting_config['critical_error_threshold'] <= 0:
            self._add_error("Error Handling: 'alerting.critical_error_threshold' must be a positive integer.")
